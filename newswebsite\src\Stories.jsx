import React, { useEffect } from 'react'

const Stories = () => {
    let isLoading = true;

    let API= "https://hn.algolia.com/api/v1/search";

    const fetchApiCall = async(url) =>{
        try{
            const response = await fetch(url);
            const data = await response.json();
            // isLoading = false;
            console.log(data);

        }
        catch(error){
            console.log(error);
        }
    }

    useEffect(()=>{
        console.log("Api calling");
        fetchApiCall(API);
    }, []);

    if(isLoading){
        return(
            <h1>Loading...</h1>
        )
    }

  return (
    <>
      <h1>My Stories</h1>
    </>
  )
}

export default Stories